import { trigger } from 'react-native-haptic-feedback';
import {
  HapticFeedbackTypes,
  HapticOptions,
} from 'react-native-haptic-feedback/lib/typescript/types';

const options: HapticOptions = {
  enableVibrateFallback: true,
  ignoreAndroidSystemSettings: false,
};

/** 触感反馈 */
export const triggerHaptic = (
  type: keyof typeof HapticFeedbackTypes = 'impactLight',
) => {
  trigger(type, options);
};
