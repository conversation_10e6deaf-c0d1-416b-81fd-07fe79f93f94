import { NavigationProp, RouteProp } from '@react-navigation/native';

export enum RouterMap {
  /** Separate Screens */
  Home = 'stack_home',
  GrudgeCreate = 'stack_grudgeCreate',
  HistoryList = 'stack_historyList',

  /** Tab Screens */
  GrudgeList = 'tab_grudgeList',
  Award = 'tab_award',
}

export type RouteParamList = {
  [RouterMap.Home]?: undefined;
  [RouterMap.HistoryList]?: ScreenReloadParams;
  [RouterMap.GrudgeList]?: ScreenReloadParams;
  [RouterMap.Award]?: undefined;
  [RouterMap.GrudgeCreate]?: undefined;
};

export type NavigationProps<R extends RouterMap> = NavigationProp<
  RouteParamList,
  R
>;

export type RouteProps<R extends RouterMap> = RouteProp<RouteParamList, R>;

export type ScreenReloadParams = {
  reload?: boolean;
};
