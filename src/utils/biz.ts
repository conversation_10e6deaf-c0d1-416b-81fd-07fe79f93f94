import { grudgeListStore } from '@/stores/grudgeListStore';
import { historyListStore } from '@/stores/historyListStore';

/** 勾消或翻旧账 */
export const checkOrRecoverItem = (id: string, type: 'check' | 'recover') => {
  const sourceStore = type === 'check' ? grudgeListStore : historyListStore;
  const targetStore = type === 'check' ? historyListStore : grudgeListStore;

  const item = sourceStore.listData.get().find((r) => r.id === id);

  if (item) {
    sourceStore.listData.set((prev) => {
      const index = prev.findIndex((r) => r.id === id);
      prev.splice(index, 1);
    });

    targetStore.listData.set((prev) => {
      prev.unshift(item);
    });
  }
};
