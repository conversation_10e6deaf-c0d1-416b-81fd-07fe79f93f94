import { useFocusEffect } from '@react-navigation/native';
import { useCallback } from 'react';

type Params = {
  onDismiss: () => void;
};

/** 当页面失焦时，执行操作 */
export const useScreenDismiss = (p: Params) => {
  const { onDismiss } = p;

  const run = useCallback(() => {
    return () => {
      onDismiss();
    };
    /* eslint-disable-next-line react-hooks/exhaustive-deps */
  }, []);

  useFocusEffect(run);
};
