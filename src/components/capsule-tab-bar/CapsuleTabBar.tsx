import { type BottomTabBarProps } from '@react-navigation/bottom-tabs';
import { isNil } from 'es-toolkit';
import { StyleSheet, Text, TouchableOpacity, View } from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';

type Props = BottomTabBarProps;

export const CapsuleTabBar = (props: Props) => {
  const { state, descriptors, navigation } = props;

  return (
    <SafeAreaView
      edges={['bottom', 'left', 'right']}
      className="bg-background"
      style={styles.tabContainer}
    >
      <View style={styles.capsuleContainer}>
        {state.routes.map((route, index) => {
          const { options } = descriptors[route.key];
          const tabBarLabel =
            typeof options.tabBarLabel === 'function'
              ? options.tabBarLabel({})
              : options.tabBarLabel;
          const label = !isNil(tabBarLabel)
            ? tabBarLabel
            : !isNil(options.title)
              ? options.title
              : route.name;

          const isFocused = state.index === index;

          const onPress = () => {
            const event = navigation.emit({
              type: 'tabPress',
              target: route.key,
              canPreventDefault: true,
            });

            if (!isFocused && !event.defaultPrevented) {
              navigation.navigate(route.name);
            }
          };

          const onLongPress = () => {
            navigation.emit({
              type: 'tabLongPress',
              target: route.key,
            });
          };

          return (
            <TouchableOpacity
              accessibilityRole="button"
              accessibilityState={isFocused ? { selected: true } : {}}
              accessibilityLabel={options.tabBarAccessibilityLabel}
              onPress={onPress}
              onLongPress={onLongPress}
              style={styles.tabItem}
              key={index}
            >
              <View
                style={[
                  styles.capsuleButton,
                  isFocused && styles.capsuleButtonFocused,
                ]}
              >
                <Text
                  style={[styles.tabLabel, isFocused && styles.tabLabelFocused]}
                >
                  {label}
                </Text>
              </View>
            </TouchableOpacity>
          );
        })}
      </View>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  tabContainer: {
    // position: 'absolute',
    // bottom: 20,
    // left: 0,
    // right: 0,
    alignItems: 'center',
    justifyContent: 'center',
  },
  capsuleContainer: {
    flexDirection: 'row',
    backgroundColor: 'rgba(255, 255, 255, 0.8)',
    borderRadius: 30,
    paddingHorizontal: 8,
    paddingVertical: 8,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
    elevation: 5,
  },
  tabItem: {
    marginHorizontal: 4,
  },
  capsuleButton: {
    paddingHorizontal: 20,
    paddingVertical: 10,
    borderRadius: 20,
    backgroundColor: 'transparent',
  },
  capsuleButtonFocused: {
    backgroundColor: '#007AFF',
  },
  tabLabel: {
    fontSize: 14,
    color: '#666',
    fontWeight: '500',
  },
  tabLabelFocused: {
    color: '#FFFFFF',
    fontWeight: '600',
  },
});
