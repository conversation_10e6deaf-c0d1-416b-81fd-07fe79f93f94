module.exports = {
  root: true,
  parser: '@typescript-eslint/parser',
  parserOptions: {
    ecmaVersion: 'es2024',
  },
  extends: [
    '@react-native',
    'plugin:prettier/recommended',
    'plugin:import-x/recommended',
    'plugin:import-x/typescript',
  ],
  plugins: ['@stylistic', 'import-x'],
  settings: {
    'import-x/resolver': {
      // This will use the TypeScript config
      typescript: {
        // this loads <rootdir>/tsconfig.json to eslint
      },
    },
  },
  rules: {
    '@typescript-eslint/no-unused-vars': 'warn',
    '@typescript-eslint/consistent-type-imports': [
      'warn',
      {
        disallowTypeAnnotations: true,
        fixStyle: 'inline-type-imports',
        prefer: 'type-imports',
      },
    ],
    quotes: ['warn', 'single'],
    'prettier/prettier': 'warn',
    'arrow-body-style': 'off',
    'prefer-arrow-callback': 'off',
    '@stylistic/jsx-curly-brace-presence': ['warn', 'never'],
  },
  globals: {
    __DEV__: true,
  },
};
