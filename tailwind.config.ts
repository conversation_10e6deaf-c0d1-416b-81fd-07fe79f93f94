import type { Config } from 'tailwindcss';

export default {
  content: ['./src/**/*.{js,jsx,ts,tsx}'],
  presets: [require('nativewind/preset')],
  theme: {
    extend: {
      colors: {
        // 基础颜色
        background: 'rgb(var(--background) / <alpha-value>)',
        foreground: 'rgb(var(--foreground) / <alpha-value>)',
        card: 'rgb(var(--card) / <alpha-value>)',
        'card-foreground': 'rgb(var(--card-foreground) / <alpha-value>)',
        popover: 'rgb(var(--popover) / <alpha-value>)',
        'popover-foreground': 'rgb(var(--popover-foreground) / <alpha-value>)',
        primary: 'rgb(var(--primary) / <alpha-value>)',
        'primary-foreground': 'rgb(var(--primary-foreground) / <alpha-value>)',
        secondary: 'rgb(var(--secondary) / <alpha-value>)',
        'secondary-foreground':
          'rgb(var(--secondary-foreground) / <alpha-value>)',
        muted: 'rgb(var(--muted) / <alpha-value>)',
        'muted-foreground': 'rgb(var(--muted-foreground) / <alpha-value>)',
        accent: 'rgb(var(--accent) / <alpha-value>)',
        'accent-foreground': 'rgb(var(--accent-foreground) / <alpha-value>)',
        destructive: 'rgb(var(--destructive) / <alpha-value>)',
        'destructive-foreground':
          'rgb(var(--destructive-foreground) / <alpha-value>)',
        border: 'rgb(var(--border) / <alpha-value>)',
        input: 'rgb(var(--input) / <alpha-value>)',
        ring: 'rgb(var(--ring) / <alpha-value>)',

        // Android 特定颜色
        'android-background': 'rgb(var(--android-background) / <alpha-value>)',
        'android-foreground': 'rgb(var(--android-foreground) / <alpha-value>)',
        'android-card': 'rgb(var(--android-card) / <alpha-value>)',
        'android-card-foreground':
          'rgb(var(--android-card-foreground) / <alpha-value>)',
        'android-popover': 'rgb(var(--android-popover) / <alpha-value>)',
        'android-popover-foreground':
          'rgb(var(--android-popover-foreground) / <alpha-value>)',
        'android-primary': 'rgb(var(--android-primary) / <alpha-value>)',
        'android-primary-foreground':
          'rgb(var(--android-primary-foreground) / <alpha-value>)',
        'android-secondary': 'rgb(var(--android-secondary) / <alpha-value>)',
        'android-secondary-foreground':
          'rgb(var(--android-secondary-foreground) / <alpha-value>)',
        'android-muted': 'rgb(var(--android-muted) / <alpha-value>)',
        'android-muted-foreground':
          'rgb(var(--android-muted-foreground) / <alpha-value>)',
        'android-accent': 'rgb(var(--android-accent) / <alpha-value>)',
        'android-accent-foreground':
          'rgb(var(--android-accent-foreground) / <alpha-value>)',
        'android-destructive':
          'rgb(var(--android-destructive) / <alpha-value>)',
        'android-destructive-foreground':
          'rgb(var(--android-destructive-foreground) / <alpha-value>)',
        'android-border': 'rgb(var(--android-border) / <alpha-value>)',
        'android-input': 'rgb(var(--android-input) / <alpha-value>)',
        'android-ring': 'rgb(var(--android-ring) / <alpha-value>)',

        // Web 特定颜色
        'web-background': 'rgb(var(--web-background) / <alpha-value>)',
        'web-foreground': 'rgb(var(--web-foreground) / <alpha-value>)',
        'web-card': 'rgb(var(--web-card) / <alpha-value>)',
        'web-card-foreground':
          'rgb(var(--web-card-foreground) / <alpha-value>)',
        'web-popover': 'rgb(var(--web-popover) / <alpha-value>)',
        'web-popover-foreground':
          'rgb(var(--web-popover-foreground) / <alpha-value>)',
        'web-primary': 'rgb(var(--web-primary) / <alpha-value>)',
        'web-primary-foreground':
          'rgb(var(--web-primary-foreground) / <alpha-value>)',
        'web-secondary': 'rgb(var(--web-secondary) / <alpha-value>)',
        'web-secondary-foreground':
          'rgb(var(--web-secondary-foreground) / <alpha-value>)',
        'web-muted': 'rgb(var(--web-muted) / <alpha-value>)',
        'web-muted-foreground':
          'rgb(var(--web-muted-foreground) / <alpha-value>)',
        'web-accent': 'rgb(var(--web-accent) / <alpha-value>)',
        'web-accent-foreground':
          'rgb(var(--web-accent-foreground) / <alpha-value>)',
        'web-destructive': 'rgb(var(--web-destructive) / <alpha-value>)',
        'web-destructive-foreground':
          'rgb(var(--web-destructive-foreground) / <alpha-value>)',
        'web-border': 'rgb(var(--web-border) / <alpha-value>)',
        'web-input': 'rgb(var(--web-input) / <alpha-value>)',
        'web-ring': 'rgb(var(--web-ring) / <alpha-value>)',
      },
    },
  },
  plugins: [],
} satisfies Config;
